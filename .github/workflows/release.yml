name: publish release version

on:
  push:
    tags:
      - "v*"

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: pnpm/action-setup@v4
        with:
          version: latest
      - uses: actions/setup-node@v4
        with:
          node-version: latest
          cache: "pnpm"
      - run: pnpm install
      - run: pnpm build+zip
      - uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: build
  deploy-web:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: build
      - name: Deploy to GitHub Pages
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          folder: build/web
  create-release:
    needs: build
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create-release.outputs.upload_url }}
    steps:
      - uses: actions/create-release@v1
        id: create-release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
  upload-release:
    needs: [build, create-release]
    strategy:
      matrix:
        client: ["chrome", "edge", "firefox", "userscript", "thunderbird"]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: build
      - uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./build/${{ matrix.client }}.zip
          asset_name: kiss-translator_${{ github.ref_name }}_${{ matrix.client }}.zip
          asset_content_type: application/zip
