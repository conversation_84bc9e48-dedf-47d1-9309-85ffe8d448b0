<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>%REACT_APP_NAME%</title>
    <style>
      img {
        width: 1.2em;
        height: 1.2em;
      }

      svg {
        max-width: 1.2em;
        max-height: 1.2em;
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // (() => {
        //   var shadow = document.querySelector("#shadow1");
        //   var root = shadow.attachShadow({ mode: "open" });
        //   var newLine = document.createElement("p");
        //   newLine.innerText = "new line";
        //   root.appendChild(newLine);
        // })();

        // setTimeout(function () {
        //   var shadow = document.querySelector("#shadow2");
        //   var root = shadow.attachShadow({ mode: "open" });
        // }, 1000);

        // setTimeout(() => {
        //   var newLine = document.createElement("p");
        //   newLine.innerText = "new line";
        //   var shadow = document.querySelector("#shadow2");
        //   shadow.shadowRoot.appendChild(newLine);
        // }, 2000);

        // setTimeout(() => {
        //   var newLine = document.createElement("div");
        //   newLine.innerHTML = "<p>second line</p><p>third line</p>";
        //   var shadow = document.querySelector("#shadow2");
        //   shadow.shadowRoot.appendChild(newLine);
        // }, 3000);

        // setTimeout(function () {
        //   var el = document.querySelector("h2");
        //   el.innerText = "hello world";

        //   var title = document.querySelector("#addtitle");
        //   title.innerHTML =
        //     "<div><p>second title</p><ul><li>second title</li><li><p>second title</p></li></ul></div>";
        // }, 1000);

        setTimeout(function () {
          var el = document.querySelector('h2>p>span');
          el.innerText = 'hello world';
        }, 1000);
      });
    </script>
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <p>You need to enable <code>JavaScript</code> to run <span>this app.</span></p>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <div id="content">
        <p>You need to enable JavaScript to run <span>this app.</span></p>
        The <span>embargo</span> has just lifted to confirm that AmpereOne is coming to
        Google Cloud with the C3A instances.
        <br />
        But these upcoming instances for now are only in private preview form.
        <br />
        <br />
        Needless to say I also haven't had any AmpereOne access to check out the
        performance and power efficiency of these new Arm server processors from Ampere
        Computing.
        <br />
      </div>
      <h2>
        <p>
          <span>React is a JavaScript library for building user interfaces.</span>
        </p>
      </h2>
      <hr />
      <input id="input1" style="width: 80%" />
      <hr />
      <textarea id="textarea1" style="width: 80%">test</textarea>
      <hr />
      <div id="addtitle"></div>
      <h2>Shadow 1</h2>
      <div id="shadow1"></div>
      <h2>Shadow 2</h2>
      <div id="shadow2"></div>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <h2>
        React Server Components (or RSC) is a new application architecture designed by the
        React team.
      </h2>
      <iframe
        id="iframe1"
        width="800px"
        height="600px"
        src="http://localhost:3000/index.html"></iframe>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <h2>We’ve first shared our research on RSC in an introductory talk and an RFC.</h2>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <h2>
        To recap them, we are introducing a new kind of component—Server Components—that
        run ahead of time and are excluded from your JavaScript bundle.
      </h2>
      <iframe id="iframe2" width="800px" height="600px" src="https://react.dev/"></iframe>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <div class="cont cont1">
        <h2>
          Server Components can run during the build, letting you read from the filesystem
          or fetch static content.
        </h2>
        <ul>
          <li>
            They can also run on the server, letting you access your data layer without
            having to build an API. You can pass data by props from Server Components to
            the interactive Client Components in the browser.
          </li>
          <li>以声明式编写 UI，可以让你的代码更加可靠，且方便调试。</li>
        </ul>
      </div>
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <br />
      <div class="cont cont2">
        <h2>
          Since our last update, we have merged the React Server Components RFC to ratify
          the proposal.
        </h2>
        <ul>
          <li>
            RSC combines the simple “request/response” mental model of server-centric
            Multi-Page Apps with the seamless interactivity of client-centric Single-Page
            Apps, giving you the best of both worlds.
          </li>
          <li>
            React 使创建交互式 UI
            变得轻而易举。为你应用的每一个状态设计简洁的视图，当数据变动时 React
            能高效更新并渲染合适的组件。
          </li>
          <li>以声明式编写 UI，可以让你的代码更加可靠，且方便调试。</li>
        </ul>
      </div>
    </div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
