{"manifest_version": 2, "name": "__MSG_app_name__", "description": "__MSG_app_description__", "version": "1.9.2", "default_locale": "en", "author": "Gabe<<EMAIL>>", "homepage_url": "https://github.com/fishjar/kiss-translator", "background": {"scripts": ["background.js"]}, "content_scripts": [{"js": ["content.js"], "matches": ["<all_urls>"], "all_frames": true}], "commands": {"_execute_browser_action": {"suggested_key": {"default": "Alt+K"}}, "toggleTranslate": {"suggested_key": {"default": "Alt+Q"}, "description": "__MSG_toggle_translate__"}, "openTranbox": {"suggested_key": {"default": "Alt+S"}, "description": "__MSG_open_tranbox__"}, "toggleStyle": {"suggested_key": {"default": "Alt+C"}, "description": "__MSG_toggle_style__"}, "openOptions": {"description": "__MSG_open_options__"}}, "permissions": ["<all_urls>", "storage", "contextMenus", "scripting", "declarativeNetRequest"], "icons": {"16": "images/logo16.png", "32": "images/logo32.png", "48": "images/logo48.png", "128": "images/logo128.png"}, "browser_action": {"default_icon": {"128": "images/logo128.png"}, "default_title": "__MSG_app_name__", "default_popup": "popup.html"}, "options_ui": {"page": "options.html", "open_in_tab": true}}